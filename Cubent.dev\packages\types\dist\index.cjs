"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  ANTHROPIC_DEFAULT_MAX_TOKENS: () => ANTHROPIC_DEFAULT_MAX_TOKENS,
  BEDROCK_DEFAULT_TEMPERATURE: () => BEDROCK_DEFAULT_TEMPERATURE,
  BEDROCK_MAX_TOKENS: () => BEDROCK_MAX_TOKENS,
  BEDROCK_REGIONS: () => BEDROCK_REGIONS,
  BEDROCK_REGION_INFO: () => BEDROCK_REGION_INFO,
  DEEP_SEEK_DEFAULT_TEMPERATURE: () => DEEP_SEEK_DEFAULT_TEMPERATURE,
  GLAMA_DEFAULT_TEMPERATURE: () => GLAMA_DEFAULT_TEMPERATURE,
  GLOBAL_SETTINGS_KEYS: () => GLOBAL_SETTINGS_KEYS,
  GLOBAL_STATE_KEYS: () => GLOBAL_STATE_KEYS,
  IpcMessageType: () => IpcMessageType,
  IpcOrigin: () => IpcOrigin,
  LITELLM_COMPUTER_USE_MODELS: () => LITELLM_COMPUTER_USE_MODELS,
  LMSTUDIO_DEFAULT_TEMPERATURE: () => LMSTUDIO_DEFAULT_TEMPERATURE,
  MISTRAL_DEFAULT_TEMPERATURE: () => MISTRAL_DEFAULT_TEMPERATURE,
  OPENAI_AZURE_AI_INFERENCE_PATH: () => OPENAI_AZURE_AI_INFERENCE_PATH,
  OPENAI_NATIVE_DEFAULT_TEMPERATURE: () => OPENAI_NATIVE_DEFAULT_TEMPERATURE,
  OPENROUTER_DEFAULT_PROVIDER_NAME: () => OPENROUTER_DEFAULT_PROVIDER_NAME,
  OPEN_ROUTER_COMPUTER_USE_MODELS: () => OPEN_ROUTER_COMPUTER_USE_MODELS,
  OPEN_ROUTER_PROMPT_CACHING_MODELS: () => OPEN_ROUTER_PROMPT_CACHING_MODELS,
  OPEN_ROUTER_REASONING_BUDGET_MODELS: () => OPEN_ROUTER_REASONING_BUDGET_MODELS,
  OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS: () => OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS,
  ORGANIZATION_ALLOW_ALL: () => ORGANIZATION_ALLOW_ALL,
  PROVIDER_SETTINGS_KEYS: () => PROVIDER_SETTINGS_KEYS,
  RooCodeEventName: () => RooCodeEventName,
  SECRET_STATE_KEYS: () => SECRET_STATE_KEYS,
  SUBSCRIPTION_PLANS: () => SUBSCRIPTION_PLANS,
  SubscriptionStatus: () => SubscriptionStatus,
  SubscriptionTier: () => SubscriptionTier,
  TaskCommandName: () => TaskCommandName,
  TelemetryEventName: () => TelemetryEventName,
  VERTEX_REGIONS: () => VERTEX_REGIONS,
  ackSchema: () => ackSchema,
  anthropicDefaultModelId: () => anthropicDefaultModelId,
  anthropicModels: () => anthropicModels,
  appPropertiesSchema: () => appPropertiesSchema,
  azureOpenAiDefaultApiVersion: () => azureOpenAiDefaultApiVersion,
  bedrockDefaultModelId: () => bedrockDefaultModelId,
  bedrockDefaultPromptRouterModelId: () => bedrockDefaultPromptRouterModelId,
  bedrockModels: () => bedrockModels,
  chutesDefaultModelId: () => chutesDefaultModelId,
  chutesModels: () => chutesModels,
  clineAskSchema: () => clineAskSchema,
  clineAsks: () => clineAsks,
  clineMessageSchema: () => clineMessageSchema,
  clineSaySchema: () => clineSaySchema,
  clineSays: () => clineSays,
  codeActionIds: () => codeActionIds,
  codebaseIndexConfigSchema: () => codebaseIndexConfigSchema,
  codebaseIndexModelsSchema: () => codebaseIndexModelsSchema,
  codebaseIndexProviderSchema: () => codebaseIndexProviderSchema,
  commandExecutionStatusSchema: () => commandExecutionStatusSchema,
  commandIds: () => commandIds,
  contextCondenseSchema: () => contextCondenseSchema,
  customModePromptsSchema: () => customModePromptsSchema,
  customModesSettingsSchema: () => customModesSettingsSchema,
  customSupportPromptsSchema: () => customSupportPromptsSchema,
  deepSeekDefaultModelId: () => deepSeekDefaultModelId,
  deepSeekModels: () => deepSeekModels,
  experimentIds: () => experimentIds,
  experimentIdsSchema: () => experimentIdsSchema,
  experimentsSchema: () => experimentsSchema,
  geminiDefaultModelId: () => geminiDefaultModelId,
  geminiModels: () => geminiModels,
  glamaDefaultModelId: () => glamaDefaultModelId,
  glamaDefaultModelInfo: () => glamaDefaultModelInfo,
  globalSettingsSchema: () => globalSettingsSchema,
  groqDefaultModelId: () => groqDefaultModelId,
  groqModels: () => groqModels,
  groupEntrySchema: () => groupEntrySchema,
  groupOptionsSchema: () => groupOptionsSchema,
  historyItemSchema: () => historyItemSchema,
  ipcMessageSchema: () => ipcMessageSchema,
  isGlobalStateKey: () => isGlobalStateKey,
  isLanguage: () => isLanguage,
  isModelParameter: () => isModelParameter,
  isSecretStateKey: () => isSecretStateKey,
  keysOf: () => keysOf,
  languages: () => languages,
  languagesSchema: () => languagesSchema,
  litellmDefaultModelId: () => litellmDefaultModelId,
  litellmDefaultModelInfo: () => litellmDefaultModelInfo,
  mistralDefaultModelId: () => mistralDefaultModelId,
  mistralModels: () => mistralModels,
  modeConfigSchema: () => modeConfigSchema,
  modelInfoSchema: () => modelInfoSchema,
  modelParameters: () => modelParameters,
  modelParametersSchema: () => modelParametersSchema,
  openAiModelInfoSaneDefaults: () => openAiModelInfoSaneDefaults,
  openAiNativeDefaultModelId: () => openAiNativeDefaultModelId,
  openAiNativeModels: () => openAiNativeModels,
  openRouterDefaultModelId: () => openRouterDefaultModelId,
  openRouterDefaultModelInfo: () => openRouterDefaultModelInfo,
  organizationAllowListSchema: () => organizationAllowListSchema,
  organizationSettingsSchema: () => organizationSettingsSchema,
  promptComponentSchema: () => promptComponentSchema,
  providerNames: () => providerNames,
  providerNamesSchema: () => providerNamesSchema,
  providerSettingsEntrySchema: () => providerSettingsEntrySchema,
  providerSettingsSchema: () => providerSettingsSchema,
  providerSettingsSchemaDiscriminated: () => providerSettingsSchemaDiscriminated,
  reasoningEfforts: () => reasoningEfforts,
  reasoningEffortsSchema: () => reasoningEffortsSchema,
  requestyDefaultModelId: () => requestyDefaultModelId,
  requestyDefaultModelInfo: () => requestyDefaultModelInfo,
  rooCodeEventsSchema: () => rooCodeEventsSchema,
  rooCodeSettingsSchema: () => rooCodeSettingsSchema,
  rooCodeTelemetryEventSchema: () => rooCodeTelemetryEventSchema,
  taskCommandSchema: () => taskCommandSchema,
  taskEventSchema: () => taskEventSchema,
  taskPropertiesSchema: () => taskPropertiesSchema,
  telemetryPropertiesSchema: () => telemetryPropertiesSchema,
  telemetrySettings: () => telemetrySettings,
  telemetrySettingsSchema: () => telemetrySettingsSchema,
  terminalActionIds: () => terminalActionIds,
  tokenUsageSchema: () => tokenUsageSchema,
  toolGroups: () => toolGroups,
  toolGroupsSchema: () => toolGroupsSchema,
  toolNames: () => toolNames,
  toolNamesSchema: () => toolNamesSchema,
  toolProgressStatusSchema: () => toolProgressStatusSchema,
  toolUsageSchema: () => toolUsageSchema,
  unboundDefaultModelId: () => unboundDefaultModelId,
  unboundDefaultModelInfo: () => unboundDefaultModelInfo,
  usageMetricsSchema: () => usageMetricsSchema,
  usageQuotasSchema: () => usageQuotasSchema,
  userPreferencesSchema: () => userPreferencesSchema,
  userProfileSchema: () => userProfileSchema,
  vertexDefaultModelId: () => vertexDefaultModelId,
  vertexModels: () => vertexModels,
  vscodeLlmDefaultModelId: () => vscodeLlmDefaultModelId,
  vscodeLlmModels: () => vscodeLlmModels,
  xaiDefaultModelId: () => xaiDefaultModelId,
  xaiModels: () => xaiModels
});
module.exports = __toCommonJS(index_exports);

// src/providers/anthropic.ts
var anthropicDefaultModelId = "claude-sonnet-4-20250514";
var anthropicModels = {
  "claude-sonnet-4-20250514": {
    maxTokens: 64e3,
    // Overridden to 8k if `enableReasoningEffort` is false.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3,
    // $0.30 per million tokens
    supportsReasoningBudget: true
  },
  "claude-opus-4-20250514": {
    maxTokens: 32e3,
    // Overridden to 8k if `enableReasoningEffort` is false.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    // $15 per million input tokens
    outputPrice: 75,
    // $75 per million output tokens
    cacheWritesPrice: 18.75,
    // $18.75 per million tokens
    cacheReadsPrice: 1.5,
    // $1.50 per million tokens
    supportsReasoningBudget: true
  },
  "claude-3-7-sonnet-20250219:thinking": {
    maxTokens: 128e3,
    // Unlocked by passing `beta` flag to the model. Otherwise, it's 64k.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3,
    // $0.30 per million tokens
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "claude-3-7-sonnet-20250219": {
    maxTokens: 8192,
    // Since we already have a `:thinking` virtual model we aren't setting `supportsReasoningBudget: true` here.
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3
    // $0.30 per million tokens
  },
  "claude-3-5-sonnet-20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    // $3 per million input tokens
    outputPrice: 15,
    // $15 per million output tokens
    cacheWritesPrice: 3.75,
    // $3.75 per million tokens
    cacheReadsPrice: 0.3
    // $0.30 per million tokens
  },
  "claude-3-5-haiku-20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1,
    outputPrice: 5,
    cacheWritesPrice: 1.25,
    cacheReadsPrice: 0.1
  },
  "claude-3-opus-20240229": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-haiku-20240307": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03
  }
};
var ANTHROPIC_DEFAULT_MAX_TOKENS = 8192;

// src/providers/bedrock.ts
var bedrockDefaultModelId = "anthropic.claude-sonnet-4-20250514-v1:0";
var bedrockDefaultPromptRouterModelId = "anthropic.claude-3-sonnet-20240229-v1:0";
var bedrockModels = {
  "amazon.nova-pro-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.8,
    outputPrice: 3.2,
    cacheWritesPrice: 0.8,
    // per million tokens
    cacheReadsPrice: 0.2,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "amazon.nova-pro-latency-optimized-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 1,
    outputPrice: 4,
    cacheWritesPrice: 1,
    // per million tokens
    cacheReadsPrice: 0.25,
    // per million tokens
    description: "Amazon Nova Pro with latency optimized inference"
  },
  "amazon.nova-lite-v1:0": {
    maxTokens: 5e3,
    contextWindow: 3e5,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.06,
    outputPrice: 0.24,
    cacheWritesPrice: 0.06,
    // per million tokens
    cacheReadsPrice: 0.015,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "amazon.nova-micro-v1:0": {
    maxTokens: 5e3,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: true,
    inputPrice: 0.035,
    outputPrice: 0.14,
    cacheWritesPrice: 0.035,
    // per million tokens
    cacheReadsPrice: 875e-5,
    // per million tokens
    minTokensPerCachePoint: 1,
    maxCachePoints: 1,
    cachableFields: ["system"]
  },
  "anthropic.claude-sonnet-4-20250514-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-opus-4-20250514-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-7-sonnet-20250219-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-sonnet-20241022-v2:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    minTokensPerCachePoint: 1024,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-haiku-20241022-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.8,
    outputPrice: 4,
    cacheWritesPrice: 1,
    cacheReadsPrice: 0.08,
    minTokensPerCachePoint: 2048,
    maxCachePoints: 4,
    cachableFields: ["system", "messages", "tools"]
  },
  "anthropic.claude-3-5-sonnet-20240620-v1:0": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15
  },
  "anthropic.claude-3-opus-20240229-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15,
    outputPrice: 75
  },
  "anthropic.claude-3-sonnet-20240229-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15
  },
  "anthropic.claude-3-haiku-20240307-v1:0": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.25,
    outputPrice: 1.25
  },
  "anthropic.claude-2-1-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 8,
    outputPrice: 24,
    description: "Claude 2.1"
  },
  "anthropic.claude-2-0-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 8,
    outputPrice: 24,
    description: "Claude 2.0"
  },
  "anthropic.claude-instant-v1:0": {
    maxTokens: 4096,
    contextWindow: 1e5,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.8,
    outputPrice: 2.4,
    description: "Claude Instant"
  },
  "deepseek.r1-v1:0": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 1.35,
    outputPrice: 5.4
  },
  "meta.llama3-3-70b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.3 Instruct (70B)"
  },
  "meta.llama3-2-90b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.2 Instruct (90B)"
  },
  "meta.llama3-2-11b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: true,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.16,
    outputPrice: 0.16,
    description: "Llama 3.2 Instruct (11B)"
  },
  "meta.llama3-2-3b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.15,
    description: "Llama 3.2 Instruct (3B)"
  },
  "meta.llama3-2-1b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.1,
    description: "Llama 3.2 Instruct (1B)"
  },
  "meta.llama3-1-405b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 2.4,
    outputPrice: 2.4,
    description: "Llama 3.1 Instruct (405B)"
  },
  "meta.llama3-1-70b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.72,
    outputPrice: 0.72,
    description: "Llama 3.1 Instruct (70B)"
  },
  "meta.llama3-1-70b-instruct-latency-optimized-v1:0": {
    maxTokens: 8192,
    contextWindow: 128e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.9,
    outputPrice: 0.9,
    description: "Llama 3.1 Instruct (70B) (w/ latency optimized inference)"
  },
  "meta.llama3-1-8b-instruct-v1:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.22,
    outputPrice: 0.22,
    description: "Llama 3.1 Instruct (8B)"
  },
  "meta.llama3-70b-instruct-v1:0": {
    maxTokens: 2048,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 2.65,
    outputPrice: 3.5
  },
  "meta.llama3-8b-instruct-v1:0": {
    maxTokens: 2048,
    contextWindow: 4e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.6
  },
  "amazon.titan-text-lite-v1:0": {
    maxTokens: 4096,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.2,
    description: "Amazon Titan Text Lite"
  },
  "amazon.titan-text-express-v1:0": {
    maxTokens: 4096,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.2,
    outputPrice: 0.6,
    description: "Amazon Titan Text Express"
  },
  "amazon.titan-text-embeddings-v1:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    description: "Amazon Titan Text Embeddings"
  },
  "amazon.titan-text-embeddings-v2:0": {
    maxTokens: 8192,
    contextWindow: 8e3,
    supportsImages: false,
    supportsComputerUse: false,
    supportsPromptCache: false,
    inputPrice: 0.02,
    description: "Amazon Titan Text Embeddings V2"
  }
};
var BEDROCK_DEFAULT_TEMPERATURE = 0.3;
var BEDROCK_MAX_TOKENS = 4096;
var BEDROCK_REGION_INFO = {
  /*
   * This JSON generated by AWS's AI assistant - Amazon Q on March 29, 2025
   *
   *  - Africa (Cape Town) region does not appear to support Amazon Bedrock at this time.
   *  - Some Asia Pacific regions, such as Asia Pacific (Hong Kong) and Asia Pacific (Jakarta), are not listed among the supported regions for Bedrock services.
   *  - Middle East regions, including Middle East (Bahrain) and Middle East (UAE), are not mentioned in the list of supported regions for Bedrock. [3]
   *  - China regions (Beijing and Ningxia) are not listed as supported for Amazon Bedrock.
   *  - Some newer or specialized AWS regions may not have Bedrock support yet.
   */
  "us.": { regionId: "us-east-1", description: "US East (N. Virginia)", pattern: "us-", multiRegion: true },
  "use.": { regionId: "us-east-1", description: "US East (N. Virginia)" },
  "use1.": { regionId: "us-east-1", description: "US East (N. Virginia)" },
  "use2.": { regionId: "us-east-2", description: "US East (Ohio)" },
  "usw.": { regionId: "us-west-2", description: "US West (Oregon)" },
  "usw2.": { regionId: "us-west-2", description: "US West (Oregon)" },
  "ug.": {
    regionId: "us-gov-west-1",
    description: "AWS GovCloud (US-West)",
    pattern: "us-gov-",
    multiRegion: true
  },
  "uge1.": { regionId: "us-gov-east-1", description: "AWS GovCloud (US-East)" },
  "ugw1.": { regionId: "us-gov-west-1", description: "AWS GovCloud (US-West)" },
  "eu.": { regionId: "eu-west-1", description: "Europe (Ireland)", pattern: "eu-", multiRegion: true },
  "euw1.": { regionId: "eu-west-1", description: "Europe (Ireland)" },
  "euw2.": { regionId: "eu-west-2", description: "Europe (London)" },
  "euw3.": { regionId: "eu-west-3", description: "Europe (Paris)" },
  "euc1.": { regionId: "eu-central-1", description: "Europe (Frankfurt)" },
  "euc2.": { regionId: "eu-central-2", description: "Europe (Zurich)" },
  "eun1.": { regionId: "eu-north-1", description: "Europe (Stockholm)" },
  "eus1.": { regionId: "eu-south-1", description: "Europe (Milan)" },
  "eus2.": { regionId: "eu-south-2", description: "Europe (Spain)" },
  "ap.": {
    regionId: "ap-southeast-1",
    description: "Asia Pacific (Singapore)",
    pattern: "ap-",
    multiRegion: true
  },
  "ape1.": { regionId: "ap-east-1", description: "Asia Pacific (Hong Kong)" },
  "apne1.": { regionId: "ap-northeast-1", description: "Asia Pacific (Tokyo)" },
  "apne2.": { regionId: "ap-northeast-2", description: "Asia Pacific (Seoul)" },
  "apne3.": { regionId: "ap-northeast-3", description: "Asia Pacific (Osaka)" },
  "aps1.": { regionId: "ap-south-1", description: "Asia Pacific (Mumbai)" },
  "aps2.": { regionId: "ap-south-2", description: "Asia Pacific (Hyderabad)" },
  "apse1.": { regionId: "ap-southeast-1", description: "Asia Pacific (Singapore)" },
  "apse2.": { regionId: "ap-southeast-2", description: "Asia Pacific (Sydney)" },
  "ca.": { regionId: "ca-central-1", description: "Canada (Central)", pattern: "ca-", multiRegion: true },
  "cac1.": { regionId: "ca-central-1", description: "Canada (Central)" },
  "sa.": { regionId: "sa-east-1", description: "South America (S\xE3o Paulo)", pattern: "sa-", multiRegion: true },
  "sae1.": { regionId: "sa-east-1", description: "South America (S\xE3o Paulo)" },
  // These are not official - they weren't generated by Amazon Q nor were
  // found in the AWS documentation but another cubent contributor found apac.
  // Was needed so I've added the pattern of the other geo zones.
  "apac.": { regionId: "ap-southeast-1", description: "Default APAC region", pattern: "ap-", multiRegion: true },
  "emea.": { regionId: "eu-west-1", description: "Default EMEA region", pattern: "eu-", multiRegion: true },
  "amer.": { regionId: "us-east-1", description: "Default Americas region", pattern: "us-", multiRegion: true }
};
var BEDROCK_REGIONS = Object.values(BEDROCK_REGION_INFO).map((info) => ({ value: info.regionId, label: info.regionId })).filter((region, index, self) => index === self.findIndex((r) => r.value === region.value)).sort((a, b) => a.value.localeCompare(b.value));

// src/providers/chutes.ts
var chutesDefaultModelId = "deepseek-ai/DeepSeek-R1-0528";
var chutesModels = {
  "deepseek-ai/DeepSeek-R1-0528": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 0528 model."
  },
  "deepseek-ai/DeepSeek-R1": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 model."
  },
  "deepseek-ai/DeepSeek-V3": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 model."
  },
  "unsloth/Llama-3.3-70B-Instruct": {
    maxTokens: 32768,
    // From Groq
    contextWindow: 131072,
    // From Groq
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Llama 3.3 70B Instruct model."
  },
  "chutesai/Llama-4-Scout-17B-16E-Instruct": {
    maxTokens: 32768,
    contextWindow: 512e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "ChutesAI Llama 4 Scout 17B Instruct model, 512K context."
  },
  "unsloth/Mistral-Nemo-Instruct-2407": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Mistral Nemo Instruct model."
  },
  "unsloth/gemma-3-12b-it": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Gemma 3 12B IT model."
  },
  "NousResearch/DeepHermes-3-Llama-3-8B-Preview": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nous DeepHermes 3 Llama 3 8B Preview model."
  },
  "unsloth/gemma-3-4b-it": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Unsloth Gemma 3 4B IT model."
  },
  "nvidia/Llama-3_3-Nemotron-Super-49B-v1": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nvidia Llama 3.3 Nemotron Super 49B model."
  },
  "nvidia/Llama-3_1-Nemotron-Ultra-253B-v1": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Nvidia Llama 3.1 Nemotron Ultra 253B model."
  },
  "chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8": {
    maxTokens: 32768,
    contextWindow: 256e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "ChutesAI Llama 4 Maverick 17B Instruct FP8 model."
  },
  "deepseek-ai/DeepSeek-V3-Base": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 Base model."
  },
  "deepseek-ai/DeepSeek-R1-Zero": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 Zero model."
  },
  "deepseek-ai/DeepSeek-V3-0324": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek V3 (0324) model."
  },
  "Qwen/Qwen3-235B-A22B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 235B A22B model."
  },
  "Qwen/Qwen3-32B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 32B model."
  },
  "Qwen/Qwen3-30B-A3B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 30B A3B model."
  },
  "Qwen/Qwen3-14B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 14B model."
  },
  "Qwen/Qwen3-8B": {
    maxTokens: 32768,
    contextWindow: 40960,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Qwen3 8B model."
  },
  "microsoft/MAI-DS-R1-FP8": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Microsoft MAI-DS-R1 FP8 model."
  },
  "tngtech/DeepSeek-R1T-Chimera": {
    maxTokens: 32768,
    contextWindow: 163840,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "TNGTech DeepSeek R1T Chimera model."
  }
};

// src/providers/deepseek.ts
var deepSeekDefaultModelId = "deepseek-chat";
var deepSeekModels = {
  "deepseek-chat": {
    maxTokens: 8192,
    contextWindow: 64e3,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.27,
    // $0.27 per million tokens (cache miss)
    outputPrice: 1.1,
    // $1.10 per million tokens
    cacheWritesPrice: 0.27,
    // $0.27 per million tokens (cache miss)
    cacheReadsPrice: 0.07,
    // $0.07 per million tokens (cache hit).
    description: `DeepSeek-V3 achieves a significant breakthrough in inference speed over previous models. It tops the leaderboard among open-source models and rivals the most advanced closed-source models globally.`
  },
  "deepseek-reasoner": {
    maxTokens: 8192,
    contextWindow: 64e3,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.55,
    // $0.55 per million tokens (cache miss)
    outputPrice: 2.19,
    // $2.19 per million tokens
    cacheWritesPrice: 0.55,
    // $0.55 per million tokens (cache miss)
    cacheReadsPrice: 0.14,
    // $0.14 per million tokens (cache hit)
    description: `DeepSeek-R1 achieves performance comparable to OpenAI-o1 across math, code, and reasoning tasks. Supports Chain of Thought reasoning with up to 32K tokens.`
  }
};
var DEEP_SEEK_DEFAULT_TEMPERATURE = 0.6;

// src/providers/gemini.ts
var geminiDefaultModelId = "gemini-2.0-flash-001";
var geminiModels = {
  "gemini-2.5-flash-preview-04-17:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-04-17": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-flash-preview-05-20:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 3.5,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-05-20": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1
  },
  "gemini-2.5-pro-exp-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.5-pro-preview-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    // This is the pricing for prompts above 200k tokens.
    outputPrice: 15,
    cacheReadsPrice: 0.625,
    cacheWritesPrice: 4.5,
    tiers: [
      {
        contextWindow: 2e5,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625
      }
    ]
  },
  "gemini-2.5-pro-preview-05-06": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    // This is the pricing for prompts above 200k tokens.
    outputPrice: 15,
    cacheReadsPrice: 0.625,
    cacheWritesPrice: 4.5,
    tiers: [
      {
        contextWindow: 2e5,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625
      }
    ]
  },
  "gemini-2.0-flash-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025,
    cacheWritesPrice: 1
  },
  "gemini-2.0-flash-lite-preview-02-05": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-pro-exp-02-05": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-thinking-exp-01-21": {
    maxTokens: 65536,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-thinking-exp-1219": {
    maxTokens: 8192,
    contextWindow: 32767,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-exp": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-002": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    // This is the pricing for prompts above 128k tokens.
    outputPrice: 0.6,
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1,
    tiers: [
      {
        contextWindow: 128e3,
        inputPrice: 0.075,
        outputPrice: 0.3,
        cacheReadsPrice: 0.01875
      },
      {
        contextWindow: Infinity,
        inputPrice: 0.15,
        outputPrice: 0.6,
        cacheReadsPrice: 0.0375
      }
    ]
  },
  "gemini-1.5-flash-exp-0827": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-8b-exp-0827": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-pro-002": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-pro-exp-0827": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-exp-1206": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  }
};

// src/providers/glama.ts
var glamaDefaultModelId = "anthropic/claude-3-7-sonnet";
var glamaDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)"
};
var GLAMA_DEFAULT_TEMPERATURE = 0;

// src/providers/groq.ts
var groqDefaultModelId = "llama-3.3-70b-versatile";
var groqModels = {
  // Models based on API response: https://api.groq.com/openai/v1/models
  "llama-3.1-8b-instant": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 3.1 8B Instant model, 128K context."
  },
  "llama-3.3-70b-versatile": {
    maxTokens: 32768,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 3.3 70B Versatile model, 128K context."
  },
  "meta-llama/llama-4-scout-17b-16e-instruct": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 4 Scout 17B Instruct model, 128K context."
  },
  "meta-llama/llama-4-maverick-17b-128e-instruct": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Meta Llama 4 Maverick 17B Instruct model, 128K context."
  },
  "mistral-saba-24b": {
    maxTokens: 32768,
    contextWindow: 32768,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Mistral Saba 24B model, 32K context."
  },
  "qwen-qwq-32b": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "Alibaba Qwen QwQ 32B model, 128K context."
  },
  "deepseek-r1-distill-llama-70b": {
    maxTokens: 131072,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    description: "DeepSeek R1 Distill Llama 70B model, 128K context."
  }
};

// src/providers/lite-llm.ts
var litellmDefaultModelId = "claude-3-7-sonnet-20250219";
var litellmDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3
};
var LITELLM_COMPUTER_USE_MODELS = /* @__PURE__ */ new Set([
  "claude-3-5-sonnet-latest",
  "claude-opus-4-20250514",
  "claude-sonnet-4-20250514",
  "claude-3-7-sonnet-latest",
  "claude-3-7-sonnet-20250219",
  "claude-3-5-sonnet-20241022",
  "vertex_ai/claude-3-5-sonnet",
  "vertex_ai/claude-3-5-sonnet-v2",
  "vertex_ai/claude-3-5-sonnet-v2@20241022",
  "vertex_ai/claude-3-7-sonnet@20250219",
  "vertex_ai/claude-opus-4@20250514",
  "vertex_ai/claude-sonnet-4@20250514",
  "openrouter/anthropic/claude-3.5-sonnet",
  "openrouter/anthropic/claude-3.5-sonnet:beta",
  "openrouter/anthropic/claude-3.7-sonnet",
  "openrouter/anthropic/claude-3.7-sonnet:beta",
  "anthropic.claude-opus-4-20250514-v1:0",
  "anthropic.claude-sonnet-4-20250514-v1:0",
  "anthropic.claude-3-7-sonnet-20250219-v1:0",
  "anthropic.claude-3-5-sonnet-20241022-v2:0",
  "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
  "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
  "us.anthropic.claude-opus-4-20250514-v1:0",
  "us.anthropic.claude-sonnet-4-20250514-v1:0",
  "eu.anthropic.claude-3-5-sonnet-20241022-v2:0",
  "eu.anthropic.claude-3-7-sonnet-20250219-v1:0",
  "eu.anthropic.claude-opus-4-20250514-v1:0",
  "eu.anthropic.claude-sonnet-4-20250514-v1:0",
  "snowflake/claude-3-5-sonnet"
]);

// src/providers/lm-studio.ts
var LMSTUDIO_DEFAULT_TEMPERATURE = 0;

// src/providers/mistral.ts
var mistralDefaultModelId = "codestral-latest";
var mistralModels = {
  "codestral-latest": {
    maxTokens: 256e3,
    contextWindow: 256e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.9
  },
  "mistral-large-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 6
  },
  "ministral-8b-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.1
  },
  "ministral-3b-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.04,
    outputPrice: 0.04
  },
  "mistral-small-latest": {
    maxTokens: 32e3,
    contextWindow: 32e3,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.2,
    outputPrice: 0.6
  },
  "pixtral-large-latest": {
    maxTokens: 131e3,
    contextWindow: 131e3,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 6
  }
};
var MISTRAL_DEFAULT_TEMPERATURE = 0;

// src/providers/openai.ts
var openAiNativeDefaultModelId = "gpt-4.1";
var openAiNativeModels = {
  "gpt-4.1": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2,
    outputPrice: 8,
    cacheReadsPrice: 0.5
  },
  "gpt-4.1-mini": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.4,
    outputPrice: 1.6,
    cacheReadsPrice: 0.1
  },
  "gpt-4.1-nano": {
    maxTokens: 32768,
    contextWindow: 1047576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025
  },
  o3: {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o3-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    reasoningEffort: "high"
  },
  "o3-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 10,
    outputPrice: 40,
    cacheReadsPrice: 2.5,
    reasoningEffort: "low"
  },
  "o4-mini": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o4-mini-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "high"
  },
  "o4-mini-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.275,
    reasoningEffort: "low"
  },
  "o3-mini": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    supportsReasoningEffort: true,
    reasoningEffort: "medium"
  },
  "o3-mini-high": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "high"
  },
  "o3-mini-low": {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55,
    reasoningEffort: "low"
  },
  o1: {
    maxTokens: 1e5,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5
  },
  "o1-preview": {
    maxTokens: 32768,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 60,
    cacheReadsPrice: 7.5
  },
  "o1-mini": {
    maxTokens: 65536,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 1.1,
    outputPrice: 4.4,
    cacheReadsPrice: 0.55
  },
  "gpt-4.5-preview": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 75,
    outputPrice: 150,
    cacheReadsPrice: 37.5
  },
  "gpt-4o": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 10,
    cacheReadsPrice: 1.25
  },
  "gpt-4o-mini": {
    maxTokens: 16384,
    contextWindow: 128e3,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    cacheReadsPrice: 0.075
  }
};
var openAiModelInfoSaneDefaults = {
  maxTokens: -1,
  contextWindow: 128e3,
  supportsImages: true,
  supportsPromptCache: false,
  inputPrice: 0,
  outputPrice: 0
};
var azureOpenAiDefaultApiVersion = "2024-08-01-preview";
var OPENAI_NATIVE_DEFAULT_TEMPERATURE = 0;
var OPENAI_AZURE_AI_INFERENCE_PATH = "/models/chat/completions";

// src/providers/openrouter.ts
var openRouterDefaultModelId = "anthropic/claude-sonnet-4";
var openRouterDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)"
};
var OPENROUTER_DEFAULT_PROVIDER_NAME = "[default]";
var OPEN_ROUTER_PROMPT_CACHING_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3-haiku",
  "anthropic/claude-3-haiku:beta",
  "anthropic/claude-3-opus",
  "anthropic/claude-3-opus:beta",
  "anthropic/claude-3-sonnet",
  "anthropic/claude-3-sonnet:beta",
  "anthropic/claude-3.5-haiku",
  "anthropic/claude-3.5-haiku-20241022",
  "anthropic/claude-3.5-haiku-20241022:beta",
  "anthropic/claude-3.5-haiku:beta",
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3.5-sonnet-20240620",
  "anthropic/claude-3.5-sonnet-20240620:beta",
  "anthropic/claude-3.5-sonnet:beta",
  "anthropic/claude-3.7-sonnet",
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-sonnet-4",
  "anthropic/claude-opus-4",
  "google/gemini-2.5-pro-preview",
  "google/gemini-2.5-flash-preview",
  "google/gemini-2.5-flash-preview:thinking",
  "google/gemini-2.5-flash-preview-05-20",
  "google/gemini-2.5-flash-preview-05-20:thinking",
  "google/gemini-2.0-flash-001",
  "google/gemini-flash-1.5",
  "google/gemini-flash-1.5-8b"
]);
var OPEN_ROUTER_COMPUTER_USE_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3.5-sonnet:beta",
  "anthropic/claude-3.7-sonnet",
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-sonnet-4",
  "anthropic/claude-opus-4"
]);
var OPEN_ROUTER_REASONING_BUDGET_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.7-sonnet:beta",
  "anthropic/claude-3.7-sonnet:thinking",
  "anthropic/claude-opus-4",
  "anthropic/claude-sonnet-4",
  "google/gemini-2.5-flash-preview-05-20",
  "google/gemini-2.5-flash-preview-05-20:thinking"
]);
var OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS = /* @__PURE__ */ new Set([
  "anthropic/claude-3.7-sonnet:thinking",
  "google/gemini-2.5-flash-preview-05-20:thinking"
]);

// src/providers/requesty.ts
var requestyDefaultModelId = "coding/claude-4-sonnet";
var requestyDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description: "The best coding model, optimized by Requesty, and automatically routed to the fastest provider. Claude 4 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities."
};

// src/providers/unbound.ts
var unboundDefaultModelId = "anthropic/claude-3-7-sonnet-20250219";
var unboundDefaultModelInfo = {
  maxTokens: 8192,
  contextWindow: 2e5,
  supportsImages: true,
  supportsPromptCache: true,
  inputPrice: 3,
  outputPrice: 15,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3
};

// src/providers/vertex.ts
var vertexDefaultModelId = "claude-sonnet-4@20250514";
var vertexModels = {
  "gemini-2.5-flash-preview-05-20:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-05-20": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-flash-preview-04-17:thinking": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 3.5,
    maxThinkingTokens: 24576,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "gemini-2.5-flash-preview-04-17": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.5-pro-preview-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15
  },
  "gemini-2.5-pro-preview-05-06": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15
  },
  "gemini-2.5-pro-exp-03-25": {
    maxTokens: 65535,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-pro-exp-02-05": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-2.0-flash-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6
  },
  "gemini-2.0-flash-lite-001": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.075,
    outputPrice: 0.3
  },
  "gemini-2.0-flash-thinking-exp-01-21": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0
  },
  "gemini-1.5-flash-002": {
    maxTokens: 8192,
    contextWindow: 1048576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.075,
    outputPrice: 0.3
  },
  "gemini-1.5-pro-002": {
    maxTokens: 8192,
    contextWindow: 2097152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 1.25,
    outputPrice: 5
  },
  "claude-sonnet-4@20250514": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true
  },
  "claude-opus-4@20250514": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-7-sonnet@20250219:thinking": {
    maxTokens: 64e3,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3,
    supportsReasoningBudget: true,
    requiredReasoningBudget: true
  },
  "claude-3-7-sonnet@20250219": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-sonnet-v2@20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-sonnet@20240620": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 3,
    outputPrice: 15,
    cacheWritesPrice: 3.75,
    cacheReadsPrice: 0.3
  },
  "claude-3-5-haiku@20241022": {
    maxTokens: 8192,
    contextWindow: 2e5,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 1,
    outputPrice: 5,
    cacheWritesPrice: 1.25,
    cacheReadsPrice: 0.1
  },
  "claude-3-opus@20240229": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15,
    outputPrice: 75,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5
  },
  "claude-3-haiku@20240307": {
    maxTokens: 4096,
    contextWindow: 2e5,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03
  }
};
var VERTEX_REGIONS = [
  { value: "us-east5", label: "us-east5" },
  { value: "us-central1", label: "us-central1" },
  { value: "europe-west1", label: "europe-west1" },
  { value: "europe-west4", label: "europe-west4" },
  { value: "asia-southeast1", label: "asia-southeast1" }
];

// src/providers/vscode-llm.ts
var vscodeLlmDefaultModelId = "claude-3.5-sonnet";
var vscodeLlmModels = {
  "gpt-3.5-turbo": {
    contextWindow: 12114,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-3.5-turbo",
    version: "gpt-3.5-turbo-0613",
    name: "GPT 3.5 Turbo",
    supportsToolCalling: true,
    maxInputTokens: 12114
  },
  "gpt-4o-mini": {
    contextWindow: 12115,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4o-mini",
    version: "gpt-4o-mini-2024-07-18",
    name: "GPT-4o mini",
    supportsToolCalling: true,
    maxInputTokens: 12115
  },
  "gpt-4": {
    contextWindow: 28501,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4",
    version: "gpt-4-0613",
    name: "GPT 4",
    supportsToolCalling: true,
    maxInputTokens: 28501
  },
  "gpt-4-0125-preview": {
    contextWindow: 63826,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4-turbo",
    version: "gpt-4-0125-preview",
    name: "GPT 4 Turbo",
    supportsToolCalling: true,
    maxInputTokens: 63826
  },
  "gpt-4o": {
    contextWindow: 63827,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4o",
    version: "gpt-4o-2024-11-20",
    name: "GPT-4o",
    supportsToolCalling: true,
    maxInputTokens: 63827
  },
  o1: {
    contextWindow: 19827,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o1-ga",
    version: "o1-2024-12-17",
    name: "o1 (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 19827
  },
  "o3-mini": {
    contextWindow: 63827,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o3-mini",
    version: "o3-mini-2025-01-31",
    name: "o3-mini",
    supportsToolCalling: true,
    maxInputTokens: 63827
  },
  "claude-3.5-sonnet": {
    contextWindow: 81638,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "claude-3.5-sonnet",
    version: "claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    supportsToolCalling: true,
    maxInputTokens: 81638
  },
  "gemini-2.0-flash-001": {
    contextWindow: 127827,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gemini-2.0-flash",
    version: "gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    supportsToolCalling: false,
    maxInputTokens: 127827
  },
  "gemini-2.5-pro": {
    contextWindow: 63830,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gemini-2.5-pro",
    version: "gemini-2.5-pro-preview-03-25",
    name: "Gemini 2.5 Pro (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 63830
  },
  "o4-mini": {
    contextWindow: 111446,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "o4-mini",
    version: "o4-mini-2025-04-16",
    name: "o4-mini (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 111446
  },
  "gpt-4.1": {
    contextWindow: 111446,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
    family: "gpt-4.1",
    version: "gpt-4.1-2025-04-14",
    name: "GPT-4.1 (Preview)",
    supportsToolCalling: true,
    maxInputTokens: 111446
  }
};

// src/providers/xai.ts
var xaiDefaultModelId = "grok-3-mini";
var xaiModels = {
  "grok-3-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15,
    description: "xAI's Grok-3 beta model with 131K context window"
  },
  "grok-3-fast-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 25,
    description: "xAI's Grok-3 fast beta model with 131K context window"
  },
  "grok-3-mini-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.5,
    description: "xAI's Grok-3 mini beta model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3-mini-fast-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.6,
    outputPrice: 4,
    description: "xAI's Grok-3 mini fast beta model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 3,
    outputPrice: 15,
    description: "xAI's Grok-3 model with 131K context window"
  },
  "grok-3-fast": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 25,
    description: "xAI's Grok-3 fast model with 131K context window"
  },
  "grok-3-mini": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.5,
    description: "xAI's Grok-3 mini model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-3-mini-fast": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.6,
    outputPrice: 4,
    description: "xAI's Grok-3 mini fast model with 131K context window",
    supportsReasoningEffort: true
  },
  "grok-2-latest": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model - latest version with 131K context window"
  },
  "grok-2": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model with 131K context window"
  },
  "grok-2-1212": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 model (version 1212) with 131K context window"
  },
  "grok-2-vision-latest": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model - latest version with image support and 32K context window"
  },
  "grok-2-vision": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model with image support and 32K context window"
  },
  "grok-2-vision-1212": {
    maxTokens: 8192,
    contextWindow: 32768,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2,
    outputPrice: 10,
    description: "xAI's Grok-2 Vision model (version 1212) with image support and 32K context window"
  },
  "grok-vision-beta": {
    maxTokens: 8192,
    contextWindow: 8192,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 15,
    description: "xAI's Grok Vision Beta model with image support and 8K context window"
  },
  "grok-beta": {
    maxTokens: 8192,
    contextWindow: 131072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 5,
    outputPrice: 15,
    description: "xAI's Grok Beta model (legacy) with 131K context window"
  }
};

// src/codebase-index.ts
var import_zod = require("zod");
var codebaseIndexConfigSchema = import_zod.z.object({
  codebaseIndexEnabled: import_zod.z.boolean().optional(),
  codebaseIndexQdrantUrl: import_zod.z.string().optional(),
  codebaseIndexEmbedderProvider: import_zod.z.enum(["openai", "ollama"]).optional(),
  codebaseIndexEmbedderBaseUrl: import_zod.z.string().optional(),
  codebaseIndexEmbedderModelId: import_zod.z.string().optional()
});
var codebaseIndexModelsSchema = import_zod.z.object({
  openai: import_zod.z.record(import_zod.z.string(), import_zod.z.object({ dimension: import_zod.z.number() })).optional(),
  ollama: import_zod.z.record(import_zod.z.string(), import_zod.z.object({ dimension: import_zod.z.number() })).optional()
});
var codebaseIndexProviderSchema = import_zod.z.object({
  codeIndexOpenAiKey: import_zod.z.string().optional(),
  codeIndexQdrantApiKey: import_zod.z.string().optional()
});

// src/cloud.ts
var import_zod2 = require("zod");
var organizationAllowListSchema = import_zod2.z.object({
  allowAll: import_zod2.z.boolean(),
  providers: import_zod2.z.record(
    import_zod2.z.object({
      allowAll: import_zod2.z.boolean(),
      models: import_zod2.z.array(import_zod2.z.string()).optional()
    })
  )
});
var ORGANIZATION_ALLOW_ALL = {
  allowAll: true,
  providers: {}
};
var organizationSettingsSchema = import_zod2.z.object({
  version: import_zod2.z.number(),
  defaultSettings: import_zod2.z.object({
    enableCheckpoints: import_zod2.z.boolean().optional(),
    maxOpenTabsContext: import_zod2.z.number().optional(),
    maxWorkspaceFiles: import_zod2.z.number().optional(),
    showRooIgnoredFiles: import_zod2.z.boolean().optional(),
    maxReadFileLine: import_zod2.z.number().optional(),
    fuzzyMatchThreshold: import_zod2.z.number().optional()
  }).optional(),
  cloudSettings: import_zod2.z.object({
    recordTaskMessages: import_zod2.z.boolean().optional()
  }).optional(),
  allowList: organizationAllowListSchema
});

// src/experiment.ts
var import_zod3 = require("zod");
var experimentIds = ["powerSteering", "concurrentFileReads"];
var experimentIdsSchema = import_zod3.z.enum(experimentIds);
var experimentsSchema = import_zod3.z.object({
  powerSteering: import_zod3.z.boolean(),
  concurrentFileReads: import_zod3.z.boolean()
});

// src/global-settings.ts
var import_zod12 = require("zod");

// src/type-fu.ts
function keysOf() {
  return (keys) => keys;
}

// src/provider-settings.ts
var import_zod5 = require("zod");

// src/model.ts
var import_zod4 = require("zod");
var reasoningEfforts = ["low", "medium", "high"];
var reasoningEffortsSchema = import_zod4.z.enum(reasoningEfforts);
var modelParameters = ["max_tokens", "temperature", "reasoning", "include_reasoning"];
var modelParametersSchema = import_zod4.z.enum(modelParameters);
var isModelParameter = (value) => modelParameters.includes(value);
var modelInfoSchema = import_zod4.z.object({
  maxTokens: import_zod4.z.number().nullish(),
  maxThinkingTokens: import_zod4.z.number().nullish(),
  contextWindow: import_zod4.z.number(),
  supportsImages: import_zod4.z.boolean().optional(),
  supportsComputerUse: import_zod4.z.boolean().optional(),
  supportsPromptCache: import_zod4.z.boolean(),
  supportsReasoningBudget: import_zod4.z.boolean().optional(),
  requiredReasoningBudget: import_zod4.z.boolean().optional(),
  supportsReasoningEffort: import_zod4.z.boolean().optional(),
  supportedParameters: import_zod4.z.array(modelParametersSchema).optional(),
  inputPrice: import_zod4.z.number().optional(),
  outputPrice: import_zod4.z.number().optional(),
  cacheWritesPrice: import_zod4.z.number().optional(),
  cacheReadsPrice: import_zod4.z.number().optional(),
  description: import_zod4.z.string().optional(),
  reasoningEffort: reasoningEffortsSchema.optional(),
  minTokensPerCachePoint: import_zod4.z.number().optional(),
  maxCachePoints: import_zod4.z.number().optional(),
  cachableFields: import_zod4.z.array(import_zod4.z.string()).optional(),
  tiers: import_zod4.z.array(
    import_zod4.z.object({
      contextWindow: import_zod4.z.number(),
      inputPrice: import_zod4.z.number().optional(),
      outputPrice: import_zod4.z.number().optional(),
      cacheWritesPrice: import_zod4.z.number().optional(),
      cacheReadsPrice: import_zod4.z.number().optional()
    })
  ).optional()
});

// src/provider-settings.ts
var providerNames = [
  "anthropic",
  "glama",
  "openrouter",
  "bedrock",
  "vertex",
  "openai",
  "ollama",
  "vscode-lm",
  "lmstudio",
  "gemini",
  "openai-native",
  "mistral",
  "deepseek",
  "unbound",
  "requesty",
  "human-relay",
  "fake-ai",
  "xai",
  "groq",
  "chutes",
  "litellm"
];
var providerNamesSchema = import_zod5.z.enum(providerNames);
var providerSettingsEntrySchema = import_zod5.z.object({
  id: import_zod5.z.string(),
  name: import_zod5.z.string(),
  apiProvider: providerNamesSchema.optional()
});
var baseProviderSettingsSchema = import_zod5.z.object({
  includeMaxTokens: import_zod5.z.boolean().optional(),
  diffEnabled: import_zod5.z.boolean().optional(),
  fuzzyMatchThreshold: import_zod5.z.number().optional(),
  modelTemperature: import_zod5.z.number().nullish(),
  rateLimitSeconds: import_zod5.z.number().optional(),
  // Model reasoning.
  enableReasoningEffort: import_zod5.z.boolean().optional(),
  reasoningEffort: reasoningEffortsSchema.optional(),
  modelMaxTokens: import_zod5.z.number().optional(),
  modelMaxThinkingTokens: import_zod5.z.number().optional()
});
var apiModelIdProviderModelSchema = baseProviderSettingsSchema.extend({
  apiModelId: import_zod5.z.string().optional()
});
var anthropicSchema = apiModelIdProviderModelSchema.extend({
  apiKey: import_zod5.z.string().optional(),
  anthropicBaseUrl: import_zod5.z.string().optional(),
  anthropicUseAuthToken: import_zod5.z.boolean().optional()
});
var glamaSchema = baseProviderSettingsSchema.extend({
  glamaModelId: import_zod5.z.string().optional(),
  glamaApiKey: import_zod5.z.string().optional()
});
var openRouterSchema = baseProviderSettingsSchema.extend({
  openRouterApiKey: import_zod5.z.string().optional(),
  openRouterModelId: import_zod5.z.string().optional(),
  openRouterBaseUrl: import_zod5.z.string().optional(),
  openRouterSpecificProvider: import_zod5.z.string().optional(),
  openRouterUseMiddleOutTransform: import_zod5.z.boolean().optional()
});
var bedrockSchema = apiModelIdProviderModelSchema.extend({
  awsAccessKey: import_zod5.z.string().optional(),
  awsSecretKey: import_zod5.z.string().optional(),
  awsSessionToken: import_zod5.z.string().optional(),
  awsRegion: import_zod5.z.string().optional(),
  awsUseCrossRegionInference: import_zod5.z.boolean().optional(),
  awsUsePromptCache: import_zod5.z.boolean().optional(),
  awsProfile: import_zod5.z.string().optional(),
  awsUseProfile: import_zod5.z.boolean().optional(),
  awsCustomArn: import_zod5.z.string().optional(),
  awsBedrockEndpointEnabled: import_zod5.z.boolean().optional(),
  awsBedrockEndpoint: import_zod5.z.string().optional()
});
var vertexSchema = apiModelIdProviderModelSchema.extend({
  vertexKeyFile: import_zod5.z.string().optional(),
  vertexJsonCredentials: import_zod5.z.string().optional(),
  vertexProjectId: import_zod5.z.string().optional(),
  vertexRegion: import_zod5.z.string().optional()
});
var openAiSchema = baseProviderSettingsSchema.extend({
  openAiBaseUrl: import_zod5.z.string().optional(),
  openAiApiKey: import_zod5.z.string().optional(),
  openAiLegacyFormat: import_zod5.z.boolean().optional(),
  openAiR1FormatEnabled: import_zod5.z.boolean().optional(),
  openAiModelId: import_zod5.z.string().optional(),
  openAiCustomModelInfo: modelInfoSchema.nullish(),
  openAiUseAzure: import_zod5.z.boolean().optional(),
  azureApiVersion: import_zod5.z.string().optional(),
  openAiStreamingEnabled: import_zod5.z.boolean().optional(),
  openAiHostHeader: import_zod5.z.string().optional(),
  // Keep temporarily for backward compatibility during migration.
  openAiHeaders: import_zod5.z.record(import_zod5.z.string(), import_zod5.z.string()).optional()
});
var ollamaSchema = baseProviderSettingsSchema.extend({
  ollamaModelId: import_zod5.z.string().optional(),
  ollamaBaseUrl: import_zod5.z.string().optional()
});
var vsCodeLmSchema = baseProviderSettingsSchema.extend({
  vsCodeLmModelSelector: import_zod5.z.object({
    vendor: import_zod5.z.string().optional(),
    family: import_zod5.z.string().optional(),
    version: import_zod5.z.string().optional(),
    id: import_zod5.z.string().optional()
  }).optional()
});
var lmStudioSchema = baseProviderSettingsSchema.extend({
  lmStudioModelId: import_zod5.z.string().optional(),
  lmStudioBaseUrl: import_zod5.z.string().optional(),
  lmStudioDraftModelId: import_zod5.z.string().optional(),
  lmStudioSpeculativeDecodingEnabled: import_zod5.z.boolean().optional()
});
var geminiSchema = apiModelIdProviderModelSchema.extend({
  geminiApiKey: import_zod5.z.string().optional(),
  googleGeminiBaseUrl: import_zod5.z.string().optional()
});
var openAiNativeSchema = apiModelIdProviderModelSchema.extend({
  openAiNativeApiKey: import_zod5.z.string().optional(),
  openAiNativeBaseUrl: import_zod5.z.string().optional()
});
var mistralSchema = apiModelIdProviderModelSchema.extend({
  mistralApiKey: import_zod5.z.string().optional(),
  mistralCodestralUrl: import_zod5.z.string().optional()
});
var deepSeekSchema = apiModelIdProviderModelSchema.extend({
  deepSeekBaseUrl: import_zod5.z.string().optional(),
  deepSeekApiKey: import_zod5.z.string().optional()
});
var unboundSchema = baseProviderSettingsSchema.extend({
  unboundApiKey: import_zod5.z.string().optional(),
  unboundModelId: import_zod5.z.string().optional()
});
var requestySchema = baseProviderSettingsSchema.extend({
  requestyApiKey: import_zod5.z.string().optional(),
  requestyModelId: import_zod5.z.string().optional()
});
var humanRelaySchema = baseProviderSettingsSchema;
var fakeAiSchema = baseProviderSettingsSchema.extend({
  fakeAi: import_zod5.z.unknown().optional()
});
var xaiSchema = apiModelIdProviderModelSchema.extend({
  xaiApiKey: import_zod5.z.string().optional()
});
var groqSchema = apiModelIdProviderModelSchema.extend({
  groqApiKey: import_zod5.z.string().optional()
});
var chutesSchema = apiModelIdProviderModelSchema.extend({
  chutesApiKey: import_zod5.z.string().optional()
});
var litellmSchema = baseProviderSettingsSchema.extend({
  litellmBaseUrl: import_zod5.z.string().optional(),
  litellmApiKey: import_zod5.z.string().optional(),
  litellmModelId: import_zod5.z.string().optional()
});
var defaultSchema = import_zod5.z.object({
  apiProvider: import_zod5.z.undefined()
});
var providerSettingsSchemaDiscriminated = import_zod5.z.discriminatedUnion("apiProvider", [
  anthropicSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("anthropic") })),
  glamaSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("glama") })),
  openRouterSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("openrouter") })),
  bedrockSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("bedrock") })),
  vertexSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("vertex") })),
  openAiSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("openai") })),
  ollamaSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("ollama") })),
  vsCodeLmSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("vscode-lm") })),
  lmStudioSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("lmstudio") })),
  geminiSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("gemini") })),
  openAiNativeSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("openai-native") })),
  mistralSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("mistral") })),
  deepSeekSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("deepseek") })),
  unboundSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("unbound") })),
  requestySchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("requesty") })),
  humanRelaySchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("human-relay") })),
  fakeAiSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("fake-ai") })),
  xaiSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("xai") })),
  groqSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("groq") })),
  chutesSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("chutes") })),
  litellmSchema.merge(import_zod5.z.object({ apiProvider: import_zod5.z.literal("litellm") })),
  defaultSchema
]);
var providerSettingsSchema = import_zod5.z.object({
  apiProvider: providerNamesSchema.optional(),
  ...anthropicSchema.shape,
  ...glamaSchema.shape,
  ...openRouterSchema.shape,
  ...bedrockSchema.shape,
  ...vertexSchema.shape,
  ...openAiSchema.shape,
  ...ollamaSchema.shape,
  ...vsCodeLmSchema.shape,
  ...lmStudioSchema.shape,
  ...geminiSchema.shape,
  ...openAiNativeSchema.shape,
  ...mistralSchema.shape,
  ...deepSeekSchema.shape,
  ...unboundSchema.shape,
  ...requestySchema.shape,
  ...humanRelaySchema.shape,
  ...fakeAiSchema.shape,
  ...xaiSchema.shape,
  ...groqSchema.shape,
  ...chutesSchema.shape,
  ...litellmSchema.shape,
  ...codebaseIndexProviderSchema.shape
});
var PROVIDER_SETTINGS_KEYS = keysOf()([
  "apiProvider",
  // Anthropic
  "apiModelId",
  "apiKey",
  "anthropicBaseUrl",
  "anthropicUseAuthToken",
  // Glama
  "glamaModelId",
  "glamaApiKey",
  // OpenRouter
  "openRouterApiKey",
  "openRouterModelId",
  "openRouterBaseUrl",
  "openRouterSpecificProvider",
  "openRouterUseMiddleOutTransform",
  // Amazon Bedrock
  "awsAccessKey",
  "awsSecretKey",
  "awsSessionToken",
  "awsRegion",
  "awsUseCrossRegionInference",
  "awsUsePromptCache",
  "awsProfile",
  "awsUseProfile",
  "awsCustomArn",
  "awsBedrockEndpointEnabled",
  "awsBedrockEndpoint",
  // Google Vertex
  "vertexKeyFile",
  "vertexJsonCredentials",
  "vertexProjectId",
  "vertexRegion",
  // OpenAI
  "openAiBaseUrl",
  "openAiApiKey",
  "openAiLegacyFormat",
  "openAiR1FormatEnabled",
  "openAiModelId",
  "openAiCustomModelInfo",
  "openAiUseAzure",
  "azureApiVersion",
  "openAiStreamingEnabled",
  "openAiHostHeader",
  // Keep temporarily for backward compatibility during migration.
  "openAiHeaders",
  // Ollama
  "ollamaModelId",
  "ollamaBaseUrl",
  // VS Code LM
  "vsCodeLmModelSelector",
  "lmStudioModelId",
  "lmStudioBaseUrl",
  "lmStudioDraftModelId",
  "lmStudioSpeculativeDecodingEnabled",
  // Gemini
  "geminiApiKey",
  "googleGeminiBaseUrl",
  // OpenAI Native
  "openAiNativeApiKey",
  "openAiNativeBaseUrl",
  // Mistral
  "mistralApiKey",
  "mistralCodestralUrl",
  // DeepSeek
  "deepSeekBaseUrl",
  "deepSeekApiKey",
  // Unbound
  "unboundApiKey",
  "unboundModelId",
  // Requesty
  "requestyApiKey",
  "requestyModelId",
  // Code Index
  "codeIndexOpenAiKey",
  "codeIndexQdrantApiKey",
  // Reasoning
  "enableReasoningEffort",
  "reasoningEffort",
  "modelMaxTokens",
  "modelMaxThinkingTokens",
  // Generic
  "includeMaxTokens",
  "diffEnabled",
  "fuzzyMatchThreshold",
  "modelTemperature",
  "rateLimitSeconds",
  // Fake AI
  "fakeAi",
  // X.AI (Grok)
  "xaiApiKey",
  // Groq
  "groqApiKey",
  // Chutes AI
  "chutesApiKey",
  // LiteLLM
  "litellmBaseUrl",
  "litellmApiKey",
  "litellmModelId"
]);

// src/history.ts
var import_zod6 = require("zod");
var historyItemSchema = import_zod6.z.object({
  id: import_zod6.z.string(),
  number: import_zod6.z.number(),
  ts: import_zod6.z.number(),
  task: import_zod6.z.string(),
  tokensIn: import_zod6.z.number(),
  tokensOut: import_zod6.z.number(),
  cacheWrites: import_zod6.z.number().optional(),
  cacheReads: import_zod6.z.number().optional(),
  totalCost: import_zod6.z.number(),
  size: import_zod6.z.number().optional(),
  workspace: import_zod6.z.string().optional(),
  title: import_zod6.z.string().optional(),
  pinned: import_zod6.z.boolean().optional()
});

// src/telemetry.ts
var import_zod8 = require("zod");

// src/message.ts
var import_zod7 = require("zod");
var clineAsks = [
  "followup",
  "command",
  "command_output",
  "completion_result",
  "tool",
  "api_req_failed",
  "resume_task",
  "resume_completed_task",
  "mistake_limit_reached",
  "browser_action_launch",
  "use_mcp_server",
  "auto_approval_max_req_reached"
];
var clineAskSchema = import_zod7.z.enum(clineAsks);
var clineSays = [
  "error",
  "api_req_started",
  "api_req_finished",
  "api_req_retried",
  "api_req_retry_delayed",
  "api_req_deleted",
  "text",
  "reasoning",
  "completion_result",
  "user_feedback",
  "user_feedback_diff",
  "command_output",
  "shell_integration_warning",
  "browser_action",
  "browser_action_result",
  "mcp_server_request_started",
  "mcp_server_response",
  "subtask_result",
  "checkpoint_saved",
  "rooignore_error",
  "diff_error",
  "condense_context",
  "condense_context_error",
  "codebase_search_result"
];
var clineSaySchema = import_zod7.z.enum(clineSays);
var toolProgressStatusSchema = import_zod7.z.object({
  icon: import_zod7.z.string().optional(),
  text: import_zod7.z.string().optional()
});
var contextCondenseSchema = import_zod7.z.object({
  cost: import_zod7.z.number(),
  prevContextTokens: import_zod7.z.number(),
  newContextTokens: import_zod7.z.number(),
  summary: import_zod7.z.string()
});
var clineMessageSchema = import_zod7.z.object({
  ts: import_zod7.z.number(),
  type: import_zod7.z.union([import_zod7.z.literal("ask"), import_zod7.z.literal("say")]),
  ask: clineAskSchema.optional(),
  say: clineSaySchema.optional(),
  text: import_zod7.z.string().optional(),
  images: import_zod7.z.array(import_zod7.z.string()).optional(),
  partial: import_zod7.z.boolean().optional(),
  reasoning: import_zod7.z.string().optional(),
  conversationHistoryIndex: import_zod7.z.number().optional(),
  checkpoint: import_zod7.z.record(import_zod7.z.string(), import_zod7.z.unknown()).optional(),
  progressStatus: toolProgressStatusSchema.optional(),
  contextCondense: contextCondenseSchema.optional()
});
var tokenUsageSchema = import_zod7.z.object({
  totalTokensIn: import_zod7.z.number(),
  totalTokensOut: import_zod7.z.number(),
  totalCacheWrites: import_zod7.z.number().optional(),
  totalCacheReads: import_zod7.z.number().optional(),
  totalCost: import_zod7.z.number(),
  contextTokens: import_zod7.z.number()
});

// src/telemetry.ts
var telemetrySettings = ["unset", "enabled", "disabled"];
var telemetrySettingsSchema = import_zod8.z.enum(telemetrySettings);
var TelemetryEventName = /* @__PURE__ */ ((TelemetryEventName2) => {
  TelemetryEventName2["TASK_CREATED"] = "Task Created";
  TelemetryEventName2["TASK_RESTARTED"] = "Task Reopened";
  TelemetryEventName2["TASK_COMPLETED"] = "Task Completed";
  TelemetryEventName2["TASK_MESSAGE"] = "Task Message";
  TelemetryEventName2["TASK_CONVERSATION_MESSAGE"] = "Conversation Message";
  TelemetryEventName2["LLM_COMPLETION"] = "LLM Completion";
  TelemetryEventName2["MODE_SWITCH"] = "Mode Switched";
  TelemetryEventName2["TOOL_USED"] = "Tool Used";
  TelemetryEventName2["CHECKPOINT_CREATED"] = "Checkpoint Created";
  TelemetryEventName2["CHECKPOINT_RESTORED"] = "Checkpoint Restored";
  TelemetryEventName2["CHECKPOINT_DIFFED"] = "Checkpoint Diffed";
  TelemetryEventName2["CONTEXT_CONDENSED"] = "Context Condensed";
  TelemetryEventName2["SLIDING_WINDOW_TRUNCATION"] = "Sliding Window Truncation";
  TelemetryEventName2["CODE_ACTION_USED"] = "Code Action Used";
  TelemetryEventName2["PROMPT_ENHANCED"] = "Prompt Enhanced";
  TelemetryEventName2["TITLE_BUTTON_CLICKED"] = "Title Button Clicked";
  TelemetryEventName2["AUTHENTICATION_INITIATED"] = "Authentication Initiated";
  TelemetryEventName2["SCHEMA_VALIDATION_ERROR"] = "Schema Validation Error";
  TelemetryEventName2["DIFF_APPLICATION_ERROR"] = "Diff Application Error";
  TelemetryEventName2["SHELL_INTEGRATION_ERROR"] = "Shell Integration Error";
  TelemetryEventName2["CONSECUTIVE_MISTAKE_ERROR"] = "Consecutive Mistake Error";
  return TelemetryEventName2;
})(TelemetryEventName || {});
var appPropertiesSchema = import_zod8.z.object({
  appName: import_zod8.z.string(),
  appVersion: import_zod8.z.string(),
  vscodeVersion: import_zod8.z.string(),
  platform: import_zod8.z.string(),
  editorName: import_zod8.z.string(),
  language: import_zod8.z.string(),
  mode: import_zod8.z.string()
});
var taskPropertiesSchema = import_zod8.z.object({
  taskId: import_zod8.z.string().optional(),
  apiProvider: import_zod8.z.enum(providerNames).optional(),
  modelId: import_zod8.z.string().optional(),
  diffStrategy: import_zod8.z.string().optional(),
  isSubtask: import_zod8.z.boolean().optional()
});
var telemetryPropertiesSchema = import_zod8.z.object({
  ...appPropertiesSchema.shape,
  ...taskPropertiesSchema.shape
});
var rooCodeTelemetryEventSchema = import_zod8.z.discriminatedUnion("type", [
  import_zod8.z.object({
    type: import_zod8.z.enum([
      "Task Created" /* TASK_CREATED */,
      "Task Reopened" /* TASK_RESTARTED */,
      "Task Completed" /* TASK_COMPLETED */,
      "Conversation Message" /* TASK_CONVERSATION_MESSAGE */,
      "Mode Switched" /* MODE_SWITCH */,
      "Tool Used" /* TOOL_USED */,
      "Checkpoint Created" /* CHECKPOINT_CREATED */,
      "Checkpoint Restored" /* CHECKPOINT_RESTORED */,
      "Checkpoint Diffed" /* CHECKPOINT_DIFFED */,
      "Code Action Used" /* CODE_ACTION_USED */,
      "Prompt Enhanced" /* PROMPT_ENHANCED */,
      "Title Button Clicked" /* TITLE_BUTTON_CLICKED */,
      "Authentication Initiated" /* AUTHENTICATION_INITIATED */,
      "Schema Validation Error" /* SCHEMA_VALIDATION_ERROR */,
      "Diff Application Error" /* DIFF_APPLICATION_ERROR */,
      "Shell Integration Error" /* SHELL_INTEGRATION_ERROR */,
      "Consecutive Mistake Error" /* CONSECUTIVE_MISTAKE_ERROR */,
      "Context Condensed" /* CONTEXT_CONDENSED */,
      "Sliding Window Truncation" /* SLIDING_WINDOW_TRUNCATION */
    ]),
    properties: telemetryPropertiesSchema
  }),
  import_zod8.z.object({
    type: import_zod8.z.literal("Task Message" /* TASK_MESSAGE */),
    properties: import_zod8.z.object({
      ...telemetryPropertiesSchema.shape,
      taskId: import_zod8.z.string(),
      message: clineMessageSchema
    })
  }),
  import_zod8.z.object({
    type: import_zod8.z.literal("LLM Completion" /* LLM_COMPLETION */),
    properties: import_zod8.z.object({
      ...telemetryPropertiesSchema.shape,
      inputTokens: import_zod8.z.number(),
      outputTokens: import_zod8.z.number(),
      cacheReadTokens: import_zod8.z.number().optional(),
      cacheWriteTokens: import_zod8.z.number().optional(),
      cost: import_zod8.z.number().optional()
    })
  })
]);

// src/mode.ts
var import_zod10 = require("zod");

// src/tool.ts
var import_zod9 = require("zod");
var toolGroups = ["read", "edit", "browser", "command", "mcp", "modes"];
var toolGroupsSchema = import_zod9.z.enum(toolGroups);
var toolNames = [
  "execute_command",
  "read_file",
  "write_to_file",
  "apply_diff",
  "insert_content",
  "search_and_replace",
  "search_files",
  "list_files",
  "list_code_definition_names",
  "browser_action",
  "use_mcp_tool",
  "access_mcp_resource",
  "ask_followup_question",
  "attempt_completion",
  "switch_mode",
  "new_task",
  "fetch_instructions",
  "codebase_search"
];
var toolNamesSchema = import_zod9.z.enum(toolNames);
var toolUsageSchema = import_zod9.z.record(
  toolNamesSchema,
  import_zod9.z.object({
    attempts: import_zod9.z.number(),
    failures: import_zod9.z.number()
  })
);

// src/mode.ts
var groupOptionsSchema = import_zod10.z.object({
  fileRegex: import_zod10.z.string().optional().refine(
    (pattern) => {
      if (!pattern) {
        return true;
      }
      try {
        new RegExp(pattern);
        return true;
      } catch {
        return false;
      }
    },
    { message: "Invalid regular expression pattern" }
  ),
  description: import_zod10.z.string().optional()
});
var groupEntrySchema = import_zod10.z.union([toolGroupsSchema, import_zod10.z.tuple([toolGroupsSchema, groupOptionsSchema])]);
var groupEntryArraySchema = import_zod10.z.array(groupEntrySchema).refine(
  (groups) => {
    const seen = /* @__PURE__ */ new Set();
    return groups.every((group) => {
      const groupName = Array.isArray(group) ? group[0] : group;
      if (seen.has(groupName)) {
        return false;
      }
      seen.add(groupName);
      return true;
    });
  },
  { message: "Duplicate groups are not allowed" }
);
var modeConfigSchema = import_zod10.z.object({
  slug: import_zod10.z.string().regex(/^[a-zA-Z0-9-]+$/, "Slug must contain only letters numbers and dashes"),
  name: import_zod10.z.string().min(1, "Name is required"),
  roleDefinition: import_zod10.z.string().min(1, "Role definition is required"),
  whenToUse: import_zod10.z.string().optional(),
  customInstructions: import_zod10.z.string().optional(),
  groups: groupEntryArraySchema,
  source: import_zod10.z.enum(["global", "project"]).optional()
});
var customModesSettingsSchema = import_zod10.z.object({
  customModes: import_zod10.z.array(modeConfigSchema).refine(
    (modes) => {
      const slugs = /* @__PURE__ */ new Set();
      return modes.every((mode) => {
        if (slugs.has(mode.slug)) {
          return false;
        }
        slugs.add(mode.slug);
        return true;
      });
    },
    {
      message: "Duplicate mode slugs are not allowed"
    }
  )
});
var promptComponentSchema = import_zod10.z.object({
  roleDefinition: import_zod10.z.string().optional(),
  whenToUse: import_zod10.z.string().optional(),
  customInstructions: import_zod10.z.string().optional()
});
var customModePromptsSchema = import_zod10.z.record(import_zod10.z.string(), promptComponentSchema.optional());
var customSupportPromptsSchema = import_zod10.z.record(import_zod10.z.string(), import_zod10.z.string().optional());

// src/vscode.ts
var import_zod11 = require("zod");
var codeActionIds = ["explainCode", "fixCode", "improveCode", "addToContext", "newTask"];
var terminalActionIds = ["terminalAddToContext", "terminalFixCommand", "terminalExplainCommand"];
var commandIds = [
  "activationCompleted",
  "plusButtonClicked",
  "promptsButtonClicked",
  "mcpButtonClicked",
  "historyButtonClicked",
  "popoutButtonClicked",
  "accountButtonClicked",
  "settingsButtonClicked",
  "openInNewTab",
  "showHumanRelayDialog",
  "registerHumanRelayCallback",
  "unregisterHumanRelayCallback",
  "handleHumanRelayResponse",
  "newTask",
  "setCustomStoragePath",
  "focusInput",
  "acceptInput",
  "startVoiceChat",
  "stopVoiceChat"
];
var languages = [
  "ca",
  "de",
  "en",
  "es",
  "fr",
  "hi",
  "it",
  "ja",
  "ko",
  "nl",
  "pl",
  "pt-BR",
  "ru",
  "tr",
  "vi",
  "zh-CN",
  "zh-TW"
];
var languagesSchema = import_zod11.z.enum(languages);
var isLanguage = (value) => languages.includes(value);

// src/global-settings.ts
var globalSettingsSchema = import_zod12.z.object({
  currentApiConfigName: import_zod12.z.string().optional(),
  listApiConfigMeta: import_zod12.z.array(providerSettingsEntrySchema).optional(),
  pinnedApiConfigs: import_zod12.z.record(import_zod12.z.string(), import_zod12.z.boolean()).optional(),
  lastShownAnnouncementId: import_zod12.z.string().optional(),
  customInstructions: import_zod12.z.string().optional(),
  taskHistory: import_zod12.z.array(historyItemSchema).optional(),
  condensingApiConfigId: import_zod12.z.string().optional(),
  customCondensingPrompt: import_zod12.z.string().optional(),
  autoApprovalEnabled: import_zod12.z.boolean().optional(),
  alwaysAllowReadOnly: import_zod12.z.boolean().optional(),
  alwaysAllowReadOnlyOutsideWorkspace: import_zod12.z.boolean().optional(),
  alwaysAllowWrite: import_zod12.z.boolean().optional(),
  alwaysAllowWriteOutsideWorkspace: import_zod12.z.boolean().optional(),
  writeDelayMs: import_zod12.z.number().optional(),
  alwaysAllowBrowser: import_zod12.z.boolean().optional(),
  alwaysApproveResubmit: import_zod12.z.boolean().optional(),
  requestDelaySeconds: import_zod12.z.number().optional(),
  alwaysAllowMcp: import_zod12.z.boolean().optional(),
  alwaysAllowModeSwitch: import_zod12.z.boolean().optional(),
  alwaysAllowSubtasks: import_zod12.z.boolean().optional(),
  alwaysAllowExecute: import_zod12.z.boolean().optional(),
  allowedCommands: import_zod12.z.array(import_zod12.z.string()).optional(),
  allowedMaxRequests: import_zod12.z.number().nullish(),
  autoCondenseContext: import_zod12.z.boolean().optional(),
  autoCondenseContextPercent: import_zod12.z.number().optional(),
  maxConcurrentFileReads: import_zod12.z.number().optional(),
  browserToolEnabled: import_zod12.z.boolean().optional(),
  browserViewportSize: import_zod12.z.string().optional(),
  screenshotQuality: import_zod12.z.number().optional(),
  remoteBrowserEnabled: import_zod12.z.boolean().optional(),
  remoteBrowserHost: import_zod12.z.string().optional(),
  cachedChromeHostUrl: import_zod12.z.string().optional(),
  enableCheckpoints: import_zod12.z.boolean().optional(),
  ttsEnabled: import_zod12.z.boolean().optional(),
  ttsSpeed: import_zod12.z.number().optional(),
  soundEnabled: import_zod12.z.boolean().optional(),
  soundVolume: import_zod12.z.number().optional(),
  maxOpenTabsContext: import_zod12.z.number().optional(),
  maxWorkspaceFiles: import_zod12.z.number().optional(),
  showRooIgnoredFiles: import_zod12.z.boolean().optional(),
  maxReadFileLine: import_zod12.z.number().optional(),
  terminalOutputLineLimit: import_zod12.z.number().optional(),
  terminalShellIntegrationTimeout: import_zod12.z.number().optional(),
  terminalShellIntegrationDisabled: import_zod12.z.boolean().optional(),
  terminalCommandDelay: import_zod12.z.number().optional(),
  terminalPowershellCounter: import_zod12.z.boolean().optional(),
  terminalZshClearEolMark: import_zod12.z.boolean().optional(),
  terminalZshOhMy: import_zod12.z.boolean().optional(),
  terminalZshP10k: import_zod12.z.boolean().optional(),
  terminalZdotdir: import_zod12.z.boolean().optional(),
  terminalCompressProgressBar: import_zod12.z.boolean().optional(),
  rateLimitSeconds: import_zod12.z.number().optional(),
  diffEnabled: import_zod12.z.boolean().optional(),
  fuzzyMatchThreshold: import_zod12.z.number().optional(),
  experiments: experimentsSchema.optional(),
  codebaseIndexModels: codebaseIndexModelsSchema.optional(),
  codebaseIndexConfig: codebaseIndexConfigSchema.optional(),
  language: languagesSchema.optional(),
  telemetrySetting: telemetrySettingsSchema.optional(),
  mcpEnabled: import_zod12.z.boolean().optional(),
  enableMcpServerCreation: import_zod12.z.boolean().optional(),
  mode: import_zod12.z.string().optional(),
  modeApiConfigs: import_zod12.z.record(import_zod12.z.string(), import_zod12.z.string()).optional(),
  customModes: import_zod12.z.array(modeConfigSchema).optional(),
  customModePrompts: customModePromptsSchema.optional(),
  customSupportPrompts: customSupportPromptsSchema.optional(),
  enhancementApiConfigId: import_zod12.z.string().optional(),
  historyPreviewCollapsed: import_zod12.z.boolean().optional(),
  showContextButton: import_zod12.z.boolean().optional(),
  showEnhancePromptButton: import_zod12.z.boolean().optional(),
  showAddImagesButton: import_zod12.z.boolean().optional(),
  // History Management Settings
  maxChatHistoryLimit: import_zod12.z.number().optional(),
  autoDeleteOldChats: import_zod12.z.boolean().optional()
});
var GLOBAL_SETTINGS_KEYS = keysOf()([
  "currentApiConfigName",
  "listApiConfigMeta",
  "pinnedApiConfigs",
  "lastShownAnnouncementId",
  "customInstructions",
  "taskHistory",
  "condensingApiConfigId",
  "customCondensingPrompt",
  "autoApprovalEnabled",
  "alwaysAllowReadOnly",
  "alwaysAllowReadOnlyOutsideWorkspace",
  "alwaysAllowWrite",
  "alwaysAllowWriteOutsideWorkspace",
  "writeDelayMs",
  "alwaysAllowBrowser",
  "alwaysApproveResubmit",
  "requestDelaySeconds",
  "alwaysAllowMcp",
  "alwaysAllowModeSwitch",
  "alwaysAllowSubtasks",
  "alwaysAllowExecute",
  "allowedCommands",
  "allowedMaxRequests",
  "autoCondenseContext",
  "autoCondenseContextPercent",
  "maxConcurrentFileReads",
  "browserToolEnabled",
  "browserViewportSize",
  "screenshotQuality",
  "remoteBrowserEnabled",
  "remoteBrowserHost",
  "enableCheckpoints",
  "ttsEnabled",
  "ttsSpeed",
  "soundEnabled",
  "soundVolume",
  "maxOpenTabsContext",
  "maxWorkspaceFiles",
  "showRooIgnoredFiles",
  "maxReadFileLine",
  "terminalOutputLineLimit",
  "terminalShellIntegrationTimeout",
  "terminalShellIntegrationDisabled",
  "terminalCommandDelay",
  "terminalPowershellCounter",
  "terminalZshClearEolMark",
  "terminalZshOhMy",
  "terminalZshP10k",
  "terminalZdotdir",
  "terminalCompressProgressBar",
  "rateLimitSeconds",
  "diffEnabled",
  "fuzzyMatchThreshold",
  "experiments",
  "codebaseIndexModels",
  "codebaseIndexConfig",
  "language",
  "telemetrySetting",
  "mcpEnabled",
  "enableMcpServerCreation",
  "mode",
  "modeApiConfigs",
  "customModes",
  "customModePrompts",
  "customSupportPrompts",
  "enhancementApiConfigId",
  "cachedChromeHostUrl",
  "historyPreviewCollapsed",
  "showContextButton",
  "showEnhancePromptButton",
  "showAddImagesButton",
  "maxChatHistoryLimit",
  "autoDeleteOldChats"
]);
var rooCodeSettingsSchema = providerSettingsSchema.merge(globalSettingsSchema);
var SECRET_STATE_KEYS = keysOf()([
  "apiKey",
  "glamaApiKey",
  "openRouterApiKey",
  "awsAccessKey",
  "awsSecretKey",
  "awsSessionToken",
  "openAiApiKey",
  "geminiApiKey",
  "openAiNativeApiKey",
  "deepSeekApiKey",
  "mistralApiKey",
  "unboundApiKey",
  "requestyApiKey",
  "xaiApiKey",
  "groqApiKey",
  "chutesApiKey",
  "litellmApiKey",
  "codeIndexOpenAiKey",
  "codeIndexQdrantApiKey"
]);
var isSecretStateKey = (key) => SECRET_STATE_KEYS.includes(key);
var GLOBAL_STATE_KEYS = [...GLOBAL_SETTINGS_KEYS, ...PROVIDER_SETTINGS_KEYS].filter(
  (key) => !SECRET_STATE_KEYS.includes(key)
);
var isGlobalStateKey = (key) => GLOBAL_STATE_KEYS.includes(key);

// src/ipc.ts
var import_zod13 = require("zod");
var RooCodeEventName = /* @__PURE__ */ ((RooCodeEventName2) => {
  RooCodeEventName2["Message"] = "message";
  RooCodeEventName2["TaskCreated"] = "taskCreated";
  RooCodeEventName2["TaskStarted"] = "taskStarted";
  RooCodeEventName2["TaskModeSwitched"] = "taskModeSwitched";
  RooCodeEventName2["TaskPaused"] = "taskPaused";
  RooCodeEventName2["TaskUnpaused"] = "taskUnpaused";
  RooCodeEventName2["TaskAskResponded"] = "taskAskResponded";
  RooCodeEventName2["TaskAborted"] = "taskAborted";
  RooCodeEventName2["TaskSpawned"] = "taskSpawned";
  RooCodeEventName2["TaskCompleted"] = "taskCompleted";
  RooCodeEventName2["TaskTokenUsageUpdated"] = "taskTokenUsageUpdated";
  RooCodeEventName2["TaskToolFailed"] = "taskToolFailed";
  return RooCodeEventName2;
})(RooCodeEventName || {});
var rooCodeEventsSchema = import_zod13.z.object({
  ["message" /* Message */]: import_zod13.z.tuple([
    import_zod13.z.object({
      taskId: import_zod13.z.string(),
      action: import_zod13.z.union([import_zod13.z.literal("created"), import_zod13.z.literal("updated")]),
      message: clineMessageSchema
    })
  ]),
  ["taskCreated" /* TaskCreated */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskStarted" /* TaskStarted */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskModeSwitched" /* TaskModeSwitched */]: import_zod13.z.tuple([import_zod13.z.string(), import_zod13.z.string()]),
  ["taskPaused" /* TaskPaused */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskUnpaused" /* TaskUnpaused */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskAskResponded" /* TaskAskResponded */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskAborted" /* TaskAborted */]: import_zod13.z.tuple([import_zod13.z.string()]),
  ["taskSpawned" /* TaskSpawned */]: import_zod13.z.tuple([import_zod13.z.string(), import_zod13.z.string()]),
  ["taskCompleted" /* TaskCompleted */]: import_zod13.z.tuple([import_zod13.z.string(), tokenUsageSchema, toolUsageSchema]),
  ["taskTokenUsageUpdated" /* TaskTokenUsageUpdated */]: import_zod13.z.tuple([import_zod13.z.string(), tokenUsageSchema]),
  ["taskToolFailed" /* TaskToolFailed */]: import_zod13.z.tuple([import_zod13.z.string(), toolNamesSchema, import_zod13.z.string()])
});
var ackSchema = import_zod13.z.object({
  clientId: import_zod13.z.string(),
  pid: import_zod13.z.number(),
  ppid: import_zod13.z.number()
});
var TaskCommandName = /* @__PURE__ */ ((TaskCommandName2) => {
  TaskCommandName2["StartNewTask"] = "StartNewTask";
  TaskCommandName2["CancelTask"] = "CancelTask";
  TaskCommandName2["CloseTask"] = "CloseTask";
  return TaskCommandName2;
})(TaskCommandName || {});
var taskCommandSchema = import_zod13.z.discriminatedUnion("commandName", [
  import_zod13.z.object({
    commandName: import_zod13.z.literal("StartNewTask" /* StartNewTask */),
    data: import_zod13.z.object({
      configuration: rooCodeSettingsSchema,
      text: import_zod13.z.string(),
      images: import_zod13.z.array(import_zod13.z.string()).optional(),
      newTab: import_zod13.z.boolean().optional()
    })
  }),
  import_zod13.z.object({
    commandName: import_zod13.z.literal("CancelTask" /* CancelTask */),
    data: import_zod13.z.string()
  }),
  import_zod13.z.object({
    commandName: import_zod13.z.literal("CloseTask" /* CloseTask */),
    data: import_zod13.z.string()
  })
]);
var taskEventSchema = import_zod13.z.discriminatedUnion("eventName", [
  import_zod13.z.object({
    eventName: import_zod13.z.literal("message" /* Message */),
    payload: rooCodeEventsSchema.shape["message" /* Message */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskCreated" /* TaskCreated */),
    payload: rooCodeEventsSchema.shape["taskCreated" /* TaskCreated */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskStarted" /* TaskStarted */),
    payload: rooCodeEventsSchema.shape["taskStarted" /* TaskStarted */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskModeSwitched" /* TaskModeSwitched */),
    payload: rooCodeEventsSchema.shape["taskModeSwitched" /* TaskModeSwitched */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskPaused" /* TaskPaused */),
    payload: rooCodeEventsSchema.shape["taskPaused" /* TaskPaused */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskUnpaused" /* TaskUnpaused */),
    payload: rooCodeEventsSchema.shape["taskUnpaused" /* TaskUnpaused */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskAskResponded" /* TaskAskResponded */),
    payload: rooCodeEventsSchema.shape["taskAskResponded" /* TaskAskResponded */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskAborted" /* TaskAborted */),
    payload: rooCodeEventsSchema.shape["taskAborted" /* TaskAborted */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskSpawned" /* TaskSpawned */),
    payload: rooCodeEventsSchema.shape["taskSpawned" /* TaskSpawned */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskCompleted" /* TaskCompleted */),
    payload: rooCodeEventsSchema.shape["taskCompleted" /* TaskCompleted */]
  }),
  import_zod13.z.object({
    eventName: import_zod13.z.literal("taskTokenUsageUpdated" /* TaskTokenUsageUpdated */),
    payload: rooCodeEventsSchema.shape["taskTokenUsageUpdated" /* TaskTokenUsageUpdated */]
  })
]);
var IpcMessageType = /* @__PURE__ */ ((IpcMessageType2) => {
  IpcMessageType2["Connect"] = "Connect";
  IpcMessageType2["Disconnect"] = "Disconnect";
  IpcMessageType2["Ack"] = "Ack";
  IpcMessageType2["TaskCommand"] = "TaskCommand";
  IpcMessageType2["TaskEvent"] = "TaskEvent";
  return IpcMessageType2;
})(IpcMessageType || {});
var IpcOrigin = /* @__PURE__ */ ((IpcOrigin2) => {
  IpcOrigin2["Client"] = "client";
  IpcOrigin2["Server"] = "server";
  return IpcOrigin2;
})(IpcOrigin || {});
var ipcMessageSchema = import_zod13.z.discriminatedUnion("type", [
  import_zod13.z.object({
    type: import_zod13.z.literal("Ack" /* Ack */),
    origin: import_zod13.z.literal("server" /* Server */),
    data: ackSchema
  }),
  import_zod13.z.object({
    type: import_zod13.z.literal("TaskCommand" /* TaskCommand */),
    origin: import_zod13.z.literal("client" /* Client */),
    clientId: import_zod13.z.string(),
    data: taskCommandSchema
  }),
  import_zod13.z.object({
    type: import_zod13.z.literal("TaskEvent" /* TaskEvent */),
    origin: import_zod13.z.literal("server" /* Server */),
    relayClientId: import_zod13.z.string().optional(),
    data: taskEventSchema
  })
]);

// src/terminal.ts
var import_zod14 = require("zod");
var commandExecutionStatusSchema = import_zod14.z.discriminatedUnion("status", [
  import_zod14.z.object({
    executionId: import_zod14.z.string(),
    status: import_zod14.z.literal("started"),
    pid: import_zod14.z.number().optional(),
    command: import_zod14.z.string()
  }),
  import_zod14.z.object({
    executionId: import_zod14.z.string(),
    status: import_zod14.z.literal("output"),
    output: import_zod14.z.string()
  }),
  import_zod14.z.object({
    executionId: import_zod14.z.string(),
    status: import_zod14.z.literal("exited"),
    exitCode: import_zod14.z.number().optional()
  }),
  import_zod14.z.object({
    executionId: import_zod14.z.string(),
    status: import_zod14.z.literal("fallback")
  })
]);

// src/user-management.ts
var import_zod15 = require("zod");
var SubscriptionTier = /* @__PURE__ */ ((SubscriptionTier2) => {
  SubscriptionTier2["FREE_TRIAL"] = "free_trial";
  SubscriptionTier2["BASIC"] = "basic";
  SubscriptionTier2["PRO"] = "pro";
  SubscriptionTier2["ENTERPRISE"] = "enterprise";
  return SubscriptionTier2;
})(SubscriptionTier || {});
var SubscriptionStatus = /* @__PURE__ */ ((SubscriptionStatus2) => {
  SubscriptionStatus2["ACTIVE"] = "active";
  SubscriptionStatus2["TRIAL"] = "trial";
  SubscriptionStatus2["EXPIRED"] = "expired";
  SubscriptionStatus2["CANCELLED"] = "cancelled";
  SubscriptionStatus2["SUSPENDED"] = "suspended";
  return SubscriptionStatus2;
})(SubscriptionStatus || {});
var SUBSCRIPTION_PLANS = {
  ["free_trial" /* FREE_TRIAL */]: {
    monthlyTokenLimit: 1e5,
    // 100K tokens
    monthlyCostLimit: 10,
    // $10
    hourlyRequestLimit: 50,
    dailyRequestLimit: 500,
    maxContextWindow: 32e3,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "GPT-4o Mini",
      "Gemini 1.5 Flash"
    ],
    canUseReasoningModels: false,
    canUseCodebaseIndex: false,
    canUseCustomModes: false,
    canExportHistory: false
  },
  ["basic" /* BASIC */]: {
    monthlyTokenLimit: 1e6,
    // 1M tokens
    monthlyCostLimit: 50,
    // $50
    hourlyRequestLimit: 200,
    dailyRequestLimit: 2e3,
    maxContextWindow: 128e3,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "Claude Sonnet 4",
      "GPT-4o",
      "GPT-4o Mini",
      "Gemini 1.5 Pro",
      "Gemini 1.5 Flash"
    ],
    canUseReasoningModels: false,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  },
  ["pro" /* PRO */]: {
    monthlyTokenLimit: 5e6,
    // 5M tokens
    monthlyCostLimit: 200,
    // $200
    hourlyRequestLimit: 500,
    dailyRequestLimit: 5e3,
    maxContextWindow: 2e5,
    allowedModels: [
      "Claude 3.5 Sonnet",
      "Claude Sonnet 4",
      "Claude 3.7 Sonnet (Thinking)",
      "GPT-4o",
      "GPT-4o Mini",
      "o1-preview",
      "o1-mini",
      "Gemini 1.5 Pro",
      "Gemini 2.0 Pro",
      "DeepSeek V3"
    ],
    canUseReasoningModels: true,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  },
  ["enterprise" /* ENTERPRISE */]: {
    monthlyTokenLimit: 2e7,
    // 20M tokens
    monthlyCostLimit: 1e3,
    // $1000
    hourlyRequestLimit: 2e3,
    dailyRequestLimit: 2e4,
    maxContextWindow: 1e6,
    allowedModels: [],
    // All models allowed
    canUseReasoningModels: true,
    canUseCodebaseIndex: true,
    canUseCustomModes: true,
    canExportHistory: true
  }
};
var usageQuotasSchema = import_zod15.z.object({
  monthlyTokenLimit: import_zod15.z.number().min(0),
  monthlyCostLimit: import_zod15.z.number().min(0),
  hourlyRequestLimit: import_zod15.z.number().min(0),
  dailyRequestLimit: import_zod15.z.number().min(0),
  maxContextWindow: import_zod15.z.number().min(0),
  allowedModels: import_zod15.z.array(import_zod15.z.string()),
  canUseReasoningModels: import_zod15.z.boolean(),
  canUseCodebaseIndex: import_zod15.z.boolean(),
  canUseCustomModes: import_zod15.z.boolean(),
  canExportHistory: import_zod15.z.boolean()
});
var usageMetricsSchema = import_zod15.z.object({
  currentMonthTokens: import_zod15.z.number().min(0),
  currentMonthCost: import_zod15.z.number().min(0),
  currentHourRequests: import_zod15.z.number().min(0),
  currentDayRequests: import_zod15.z.number().min(0),
  totalTokensUsed: import_zod15.z.number().min(0),
  totalCostAccrued: import_zod15.z.number().min(0),
  totalRequestsMade: import_zod15.z.number().min(0),
  lastMonthlyReset: import_zod15.z.date(),
  lastHourlyReset: import_zod15.z.date(),
  lastDailyReset: import_zod15.z.date(),
  modelUsage: import_zod15.z.record(import_zod15.z.object({
    tokens: import_zod15.z.number().min(0),
    cost: import_zod15.z.number().min(0),
    requests: import_zod15.z.number().min(0)
  }))
});
var userPreferencesSchema = import_zod15.z.object({
  usageWarningsEnabled: import_zod15.z.boolean(),
  trialExpiryNotifications: import_zod15.z.boolean(),
  detailedUsageTracking: import_zod15.z.boolean(),
  costAlertsEnabled: import_zod15.z.boolean(),
  costAlertThreshold: import_zod15.z.number().min(0).max(100),
  autoUpgradeEnabled: import_zod15.z.boolean(),
  preferredUpgradeTier: import_zod15.z.nativeEnum(SubscriptionTier)
});
var userProfileSchema = import_zod15.z.object({
  id: import_zod15.z.string(),
  email: import_zod15.z.string().email(),
  name: import_zod15.z.string().optional(),
  picture: import_zod15.z.string().optional(),
  subscriptionTier: import_zod15.z.nativeEnum(SubscriptionTier),
  subscriptionStatus: import_zod15.z.nativeEnum(SubscriptionStatus),
  subscriptionStartDate: import_zod15.z.date(),
  subscriptionEndDate: import_zod15.z.date().optional(),
  trialStartDate: import_zod15.z.date().optional(),
  trialEndDate: import_zod15.z.date().optional(),
  trialExtensions: import_zod15.z.number().min(0),
  quotas: usageQuotasSchema,
  usage: usageMetricsSchema,
  preferences: userPreferencesSchema,
  createdAt: import_zod15.z.date(),
  updatedAt: import_zod15.z.date(),
  lastActiveAt: import_zod15.z.date()
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ANTHROPIC_DEFAULT_MAX_TOKENS,
  BEDROCK_DEFAULT_TEMPERATURE,
  BEDROCK_MAX_TOKENS,
  BEDROCK_REGIONS,
  BEDROCK_REGION_INFO,
  DEEP_SEEK_DEFAULT_TEMPERATURE,
  GLAMA_DEFAULT_TEMPERATURE,
  GLOBAL_SETTINGS_KEYS,
  GLOBAL_STATE_KEYS,
  IpcMessageType,
  IpcOrigin,
  LITELLM_COMPUTER_USE_MODELS,
  LMSTUDIO_DEFAULT_TEMPERATURE,
  MISTRAL_DEFAULT_TEMPERATURE,
  OPENAI_AZURE_AI_INFERENCE_PATH,
  OPENAI_NATIVE_DEFAULT_TEMPERATURE,
  OPENROUTER_DEFAULT_PROVIDER_NAME,
  OPEN_ROUTER_COMPUTER_USE_MODELS,
  OPEN_ROUTER_PROMPT_CACHING_MODELS,
  OPEN_ROUTER_REASONING_BUDGET_MODELS,
  OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS,
  ORGANIZATION_ALLOW_ALL,
  PROVIDER_SETTINGS_KEYS,
  RooCodeEventName,
  SECRET_STATE_KEYS,
  SUBSCRIPTION_PLANS,
  SubscriptionStatus,
  SubscriptionTier,
  TaskCommandName,
  TelemetryEventName,
  VERTEX_REGIONS,
  ackSchema,
  anthropicDefaultModelId,
  anthropicModels,
  appPropertiesSchema,
  azureOpenAiDefaultApiVersion,
  bedrockDefaultModelId,
  bedrockDefaultPromptRouterModelId,
  bedrockModels,
  chutesDefaultModelId,
  chutesModels,
  clineAskSchema,
  clineAsks,
  clineMessageSchema,
  clineSaySchema,
  clineSays,
  codeActionIds,
  codebaseIndexConfigSchema,
  codebaseIndexModelsSchema,
  codebaseIndexProviderSchema,
  commandExecutionStatusSchema,
  commandIds,
  contextCondenseSchema,
  customModePromptsSchema,
  customModesSettingsSchema,
  customSupportPromptsSchema,
  deepSeekDefaultModelId,
  deepSeekModels,
  experimentIds,
  experimentIdsSchema,
  experimentsSchema,
  geminiDefaultModelId,
  geminiModels,
  glamaDefaultModelId,
  glamaDefaultModelInfo,
  globalSettingsSchema,
  groqDefaultModelId,
  groqModels,
  groupEntrySchema,
  groupOptionsSchema,
  historyItemSchema,
  ipcMessageSchema,
  isGlobalStateKey,
  isLanguage,
  isModelParameter,
  isSecretStateKey,
  keysOf,
  languages,
  languagesSchema,
  litellmDefaultModelId,
  litellmDefaultModelInfo,
  mistralDefaultModelId,
  mistralModels,
  modeConfigSchema,
  modelInfoSchema,
  modelParameters,
  modelParametersSchema,
  openAiModelInfoSaneDefaults,
  openAiNativeDefaultModelId,
  openAiNativeModels,
  openRouterDefaultModelId,
  openRouterDefaultModelInfo,
  organizationAllowListSchema,
  organizationSettingsSchema,
  promptComponentSchema,
  providerNames,
  providerNamesSchema,
  providerSettingsEntrySchema,
  providerSettingsSchema,
  providerSettingsSchemaDiscriminated,
  reasoningEfforts,
  reasoningEffortsSchema,
  requestyDefaultModelId,
  requestyDefaultModelInfo,
  rooCodeEventsSchema,
  rooCodeSettingsSchema,
  rooCodeTelemetryEventSchema,
  taskCommandSchema,
  taskEventSchema,
  taskPropertiesSchema,
  telemetryPropertiesSchema,
  telemetrySettings,
  telemetrySettingsSchema,
  terminalActionIds,
  tokenUsageSchema,
  toolGroups,
  toolGroupsSchema,
  toolNames,
  toolNamesSchema,
  toolProgressStatusSchema,
  toolUsageSchema,
  unboundDefaultModelId,
  unboundDefaultModelInfo,
  usageMetricsSchema,
  usageQuotasSchema,
  userPreferencesSchema,
  userProfileSchema,
  vertexDefaultModelId,
  vertexModels,
  vscodeLlmDefaultModelId,
  vscodeLlmModels,
  xaiDefaultModelId,
  xaiModels
});
//# sourceMappingURL=index.cjs.map